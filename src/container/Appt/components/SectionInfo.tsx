import { Form, Text, cn } from '@moego/ui';
import React from 'react';

export interface SectionInfoProps {
  label: React.ReactNode;
  className?: string;
  childClassName?: string;
  flexDirection?: 'row' | 'column';
  noGap?: boolean;
  isRequired?: boolean;
}

export function SectionInfo(props: React.PropsWithChildren<SectionInfoProps>) {
  const {
    children,
    className,
    label,
    childClassName,
    flexDirection = 'column',
    noGap = false,
    isRequired = false,
    ...rest
  } = props;
  const isColumn = flexDirection === 'column';

  return (
    <div
      {...rest}
      className={cn(
        'moe-flex',
        isColumn ? 'moe-flex-col' : 'moe-flex-row moe-items-center',
        noGap ? '' : isColumn ? 'moe-gap-y-[4px]' : 'moe-gap-x-[8px]',
        className,
      )}
    >
      {typeof label === 'string' ? (
        <Text variant="small" className="moe-font-bold moe-inline-flex moe-items-center">
          {label}
          {isRequired && <Form.RequiredIcon />}
        </Text>
      ) : (
        label
      )}

      <div className={childClassName}>{children}</div>
    </div>
  );
}

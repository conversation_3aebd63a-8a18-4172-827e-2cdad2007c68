import { useSelector } from 'amos';
import { useEffect } from 'react';
import { useHistory } from 'react-router';
import { PATH_ONLINE_BOOKING_NEW_SETTINGS } from '../../../../../../router/paths';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';

/** fix switch biz may be stay on previous page */
export function useMobileRouteProtect(
  nextRoute: string = PATH_ONLINE_BOOKING_NEW_SETTINGS.build({ panel: 'landing_page' }),
) {
  const history = useHistory();
  const [business] = useSelector(selectCurrentBusiness());
  const isMobile = business.isMobileGrooming();

  useEffect(() => {
    // only apptype = mobile can stay on page
    if (!isMobile) {
      history.push(nextRoute);
    }
  }, [isMobile, nextRoute]);
}

import { MajorInfoOutlined } from '@moego/icons-react';
import { Checkbox, cn, Heading, Spin, Text, Tooltip } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import { Condition } from '../../../../../components/Condition';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';

interface ConvenienceFeesProps {
  canControl: boolean;
  amount: number;
  feeName: string;
  enabled: boolean;
  onEnabledChange: (enabled: boolean) => void;
  loading: boolean;
  className?: string;
}

export const ConvenienceFees = (props: ConvenienceFeesProps) => {
  const { canControl, amount, feeName, enabled, onEnabledChange, loading, className } = props;
  const [business] = useSelector(selectCurrentBusiness);

  return (
    <section className={cn('moe-w-full moe-flex moe-items-center moe-justify-start moe-h-5', className)}>
      <Condition if={loading}>
        <Spin className="moe-ml-xs" size="s" />
      </Condition>
      <Condition if={canControl && !loading}>
        <Checkbox
          isSelected={enabled}
          onChange={(isSelected) => {
            onEnabledChange(isSelected);
          }}
          classNames={{
            base: 'moe-flex-row-reverse moe-gap-xs moe-mr-2',
            label: 'moe-mb-0',
          }}
        />
      </Condition>
      <Condition if={!loading}>
        <div className="moe-flex moe-gap-xxs">
          <Text variant="regular-short">{feeName}</Text>
          <Tooltip content="Debit card transactions are prohibited from surcharging." side="top">
            <MajorInfoOutlined className="moe-w-[20px] moe-h-[20px] moe-cursor-pointer moe-text-tertiary" />
          </Tooltip>
        </div>
        <Heading size="5" className="moe-ml-xs">
          {business.formatAmount(amount)}
        </Heading>
      </Condition>
    </section>
  );
};

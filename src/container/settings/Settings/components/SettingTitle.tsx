import { Heading, cn } from '@moego/ui';
import React, { memo, type ReactNode } from 'react';

export interface SettingTitleProps {
  title: ReactNode;
  headerRight?: ReactNode;
  className?: string;
}

export const SettingTitle = memo(function SettingTitle(props: SettingTitleProps) {
  const { headerRight, title, className = '' } = props;
  return (
    <div className={cn(`moe-flex moe-justify-between moe-mb-[32px] ${className}`)}>
      <Heading size="2" className="moe-text-primary">
        {title}
      </Heading>
      {headerRight}
    </div>
  );
});

import { Form, Input, Modal, Text, useForm } from '@moego/ui';
import React, { memo, useEffect } from 'react';

export interface BulkAddLodgingUnitParams {
  quantity: number;
  prefix: string;
  startingNumber: number;
}

export interface BulkAddLodgingUnitModalProps {
  visible: boolean;
  onClose: () => void;
  onApply: (params: BulkAddLodgingUnitParams) => void;
}

export const BulkAddLodgingUnitModal = memo<BulkAddLodgingUnitModalProps>((props) => {
  const { visible, onClose, onApply } = props;

  const form = useForm<BulkAddLodgingUnitParams>();

  const handleApply = () => {
    form.handleSubmit((values) => {
      onApply(values);
    })();
  };

  useEffect(() => {
    if (visible) {
      form.reset({ startingNumber: 1, quantity: 10, prefix: '' });
    }
  }, [visible]);

  return (
    <Modal size="s" title="Bulk add" isOpen={visible} onClose={onClose} confirmText="Apply" onConfirm={handleApply}>
      <Text variant="small" className="moe-text-tertiary">
        Automatically assign room codes in a sequential numbering system. This provides a quick and systematic way to
        organize your rooms, such as 'Room 101,' 'Room 102,' and so on.
      </Text>

      <Form form={form} footer={null} className="moe-mt-l moe-gap-y-s">
        <Form.Item name="quantity">
          <Input.Number label="Unit quantity" step={1} minValue={1} />
        </Form.Item>
        <Form.Item name="prefix">
          <Input
            label="Prefix"
            placeholder="e.g. Room"
            tooltip="Refers to the initial part of the unit name, such as 'Room' in 'Room001,'"
          />
        </Form.Item>
        <Form.Item name="startingNumber">
          <Input.Number label="Starting number" isRequired />
        </Form.Item>
      </Form>
    </Modal>
  );
});

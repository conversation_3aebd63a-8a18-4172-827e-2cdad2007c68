package com.moego.svc.appointment.listener;

import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import com.moego.svc.appointment.listener.event.CancelAppointmentEvent;
import com.moego.svc.appointment.listener.event.CreateAppointmentEvent;
import com.moego.svc.appointment.listener.event.DeleteAppointmentPetDetailEvent;
import com.moego.svc.appointment.listener.event.SaveOrUpdatePetDetailEvent;
import com.moego.svc.appointment.listener.event.UpdateUpcomingPetDetailEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/2/21
 */
@Component
public class ActivityLogListener {

    @EventListener
    public void activityLog(CreateAppointmentEvent event) {
        ActivityLogRecorder.record(
                event.getBusinessId(),
                Boolean.TRUE.equals(event.getIsBookingRequest()) ? event.getCustomerId() : event.getStaffId(),
                AppointmentAction.CREATE,
                ResourceType.APPOINTMENT,
                event.getAppointmentId(),
                event.getAppointment());
    }

    @EventListener
    public void activityLog(SaveOrUpdatePetDetailEvent event) {
        ActivityLogRecorder.record(
                event.getBusinessId(),
                event.getTokenStaffId(),
                AppointmentAction.EDIT_PET_AND_SERVICES,
                ResourceType.APPOINTMENT,
                event.getAppointmentId(),
                event.getPetDetailDefs());
    }

    @EventListener
    public void activityLog(DeleteAppointmentPetDetailEvent event) {
        ActivityLogRecorder.record(
                event.getBusinessId(),
                event.getStaffId(),
                AppointmentAction.DELETE_PET_AND_SERVICES,
                ResourceType.APPOINTMENT,
                event.getAppointmentId(),
                event.getPetId());
    }

    @EventListener
    public void activityLog(UpdateUpcomingPetDetailEvent event) {
        ActivityLogRecorder.record(
                event.getBusinessId(),
                event.getStaffId(),
                AppointmentAction.UPDATE_SERVICES,
                ResourceType.APPOINTMENT,
                event.getAppointmentId(),
                event.getServiceOverride());
    }

    @EventListener
    public void activityLog(CancelAppointmentEvent event) {
        ActivityLogRecorder.record(
                event.getBusinessId(),
                event.getCancelBy(),
                AppointmentAction.CANCEL,
                ResourceType.APPOINTMENT,
                event.getAppointmentId(),
                event.getAppointment());
    }
}

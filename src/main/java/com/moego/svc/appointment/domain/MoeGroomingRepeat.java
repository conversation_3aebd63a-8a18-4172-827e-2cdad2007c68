package com.moego.svc.appointment.domain;

import jakarta.annotation.Generated;
import java.util.Date;

/**
 * Database Table Remarks:
 *   重复预约订单信息记录表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_repeat
 */
public class MoeGroomingRepeat {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.id")
    private Integer id;

    /**
     * Database Column Remarks:
     *   客户id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.customer_id")
    private Integer customerId;

    /**
     * Database Column Remarks:
     *   商户id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.business_id")
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   商户员工id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.staff_id")
    private Integer staffId;

    /**
     * Database Column Remarks:
     *   重复类型 1 day 2 week 3 month
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.repeat_type")
    private Byte repeatType;

    /**
     * Database Column Remarks:
     *   重复周期间隔type为1或2有此值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.repeat_every")
    private Integer repeatEvery;

    /**
     * Database Column Remarks:
     *   重复条件当type为2有此值，每周几
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.repeat_by")
    private String repeatBy;

    /**
     * Database Column Remarks:
     *   repeat开始时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.starts_on")
    private Date startsOn;

    /**
     * Database Column Remarks:
     *   重复次数（周重复使用）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.times")
    private Integer times;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.create_time")
    private Long createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.update_time")
    private Long updateTime;

    /**
     * Database Column Remarks:
     *   状态 1-正常 2-关闭
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.status")
    private Byte status;

    /**
     * Database Column Remarks:
     *   repeat 结束时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.end_on")
    private String endOn;

    /**
     * Database Column Remarks:
     *   是否开启准备过期提醒 1是 2否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.is_notice")
    private Byte isNotice;

    /**
     * Database Column Remarks:
     *   商家设置的结束时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.set_end_on")
    private Date setEndOn;

    /**
     * Database Column Remarks:
     *   type为3有此值，每月的第几天为2每月第几个星期几为1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.repeat_every_type")
    private Byte repeatEveryType;

    /**
     * Database Column Remarks:
     *   当repeat_every_type为2需指定每月的几号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.month_day")
    private Byte monthDay;

    /**
     * Database Column Remarks:
     *   当repeat_every_type为1需指定每月的第几个星期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.month_week_times")
    private Byte monthWeekTimes;

    /**
     * Database Column Remarks:
     *   当repeat_every_type为1需指定每月的周几
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.month_week_day")
    private Byte monthWeekDay;

    /**
     * Database Column Remarks:
     *   1次数2设置结束时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.type")
    private String type;

    /**
     * Database Column Remarks:
     *   ss for repeat flag: 0-normal repeat, 1-ss repeat
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.ss_flag")
    private Byte ssFlag;

    /**
     * Database Column Remarks:
     *   ss settings: before available days
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.ss_before_days")
    private Integer ssBeforeDays;

    /**
     * Database Column Remarks:
     *   ss settings: after available days
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.ss_after_days")
    private Integer ssAfterDays;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.company_id")
    private Long companyId;

    /**
     * Database Column Remarks:
     *   每周哪几天
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.repeat_by_days")
    private String repeatByDays;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.id")
    public Integer getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.id")
    public void setId(Integer id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.customer_id")
    public Integer getCustomerId() {
        return customerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.customer_id")
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.business_id")
    public Integer getBusinessId() {
        return businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.business_id")
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.staff_id")
    public Integer getStaffId() {
        return staffId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.staff_id")
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.repeat_type")
    public Byte getRepeatType() {
        return repeatType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.repeat_type")
    public void setRepeatType(Byte repeatType) {
        this.repeatType = repeatType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.repeat_every")
    public Integer getRepeatEvery() {
        return repeatEvery;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.repeat_every")
    public void setRepeatEvery(Integer repeatEvery) {
        this.repeatEvery = repeatEvery;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.repeat_by")
    public String getRepeatBy() {
        return repeatBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.repeat_by")
    public void setRepeatBy(String repeatBy) {
        this.repeatBy = repeatBy == null ? null : repeatBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.starts_on")
    public Date getStartsOn() {
        return startsOn;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.starts_on")
    public void setStartsOn(Date startsOn) {
        this.startsOn = startsOn;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.times")
    public Integer getTimes() {
        return times;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.times")
    public void setTimes(Integer times) {
        this.times = times;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.create_time")
    public Long getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.create_time")
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.update_time")
    public Long getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.update_time")
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.status")
    public Byte getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.status")
    public void setStatus(Byte status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.end_on")
    public String getEndOn() {
        return endOn;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.end_on")
    public void setEndOn(String endOn) {
        this.endOn = endOn == null ? null : endOn.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.is_notice")
    public Byte getIsNotice() {
        return isNotice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.is_notice")
    public void setIsNotice(Byte isNotice) {
        this.isNotice = isNotice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.set_end_on")
    public Date getSetEndOn() {
        return setEndOn;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.set_end_on")
    public void setSetEndOn(Date setEndOn) {
        this.setEndOn = setEndOn;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.repeat_every_type")
    public Byte getRepeatEveryType() {
        return repeatEveryType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.repeat_every_type")
    public void setRepeatEveryType(Byte repeatEveryType) {
        this.repeatEveryType = repeatEveryType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.month_day")
    public Byte getMonthDay() {
        return monthDay;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.month_day")
    public void setMonthDay(Byte monthDay) {
        this.monthDay = monthDay;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.month_week_times")
    public Byte getMonthWeekTimes() {
        return monthWeekTimes;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.month_week_times")
    public void setMonthWeekTimes(Byte monthWeekTimes) {
        this.monthWeekTimes = monthWeekTimes;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.month_week_day")
    public Byte getMonthWeekDay() {
        return monthWeekDay;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.month_week_day")
    public void setMonthWeekDay(Byte monthWeekDay) {
        this.monthWeekDay = monthWeekDay;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.type")
    public String getType() {
        return type;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.type")
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.ss_flag")
    public Byte getSsFlag() {
        return ssFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.ss_flag")
    public void setSsFlag(Byte ssFlag) {
        this.ssFlag = ssFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.ss_before_days")
    public Integer getSsBeforeDays() {
        return ssBeforeDays;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.ss_before_days")
    public void setSsBeforeDays(Integer ssBeforeDays) {
        this.ssBeforeDays = ssBeforeDays;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.ss_after_days")
    public Integer getSsAfterDays() {
        return ssAfterDays;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.ss_after_days")
    public void setSsAfterDays(Integer ssAfterDays) {
        this.ssAfterDays = ssAfterDays;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.company_id")
    public Long getCompanyId() {
        return companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.company_id")
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.repeat_by_days")
    public String getRepeatByDays() {
        return repeatByDays;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_repeat.repeat_by_days")
    public void setRepeatByDays(String repeatByDays) {
        this.repeatByDays = repeatByDays == null ? null : repeatByDays.trim();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", customerId=").append(customerId);
        sb.append(", businessId=").append(businessId);
        sb.append(", staffId=").append(staffId);
        sb.append(", repeatType=").append(repeatType);
        sb.append(", repeatEvery=").append(repeatEvery);
        sb.append(", repeatBy=").append(repeatBy);
        sb.append(", startsOn=").append(startsOn);
        sb.append(", times=").append(times);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", status=").append(status);
        sb.append(", endOn=").append(endOn);
        sb.append(", isNotice=").append(isNotice);
        sb.append(", setEndOn=").append(setEndOn);
        sb.append(", repeatEveryType=").append(repeatEveryType);
        sb.append(", monthDay=").append(monthDay);
        sb.append(", monthWeekTimes=").append(monthWeekTimes);
        sb.append(", monthWeekDay=").append(monthWeekDay);
        sb.append(", type=").append(type);
        sb.append(", ssFlag=").append(ssFlag);
        sb.append(", ssBeforeDays=").append(ssBeforeDays);
        sb.append(", ssAfterDays=").append(ssAfterDays);
        sb.append(", companyId=").append(companyId);
        sb.append(", repeatByDays=").append(repeatByDays);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MoeGroomingRepeat other = (MoeGroomingRepeat) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCustomerId() == null ? other.getCustomerId() == null : this.getCustomerId().equals(other.getCustomerId()))
            && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
            && (this.getStaffId() == null ? other.getStaffId() == null : this.getStaffId().equals(other.getStaffId()))
            && (this.getRepeatType() == null ? other.getRepeatType() == null : this.getRepeatType().equals(other.getRepeatType()))
            && (this.getRepeatEvery() == null ? other.getRepeatEvery() == null : this.getRepeatEvery().equals(other.getRepeatEvery()))
            && (this.getRepeatBy() == null ? other.getRepeatBy() == null : this.getRepeatBy().equals(other.getRepeatBy()))
            && (this.getStartsOn() == null ? other.getStartsOn() == null : this.getStartsOn().equals(other.getStartsOn()))
            && (this.getTimes() == null ? other.getTimes() == null : this.getTimes().equals(other.getTimes()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getEndOn() == null ? other.getEndOn() == null : this.getEndOn().equals(other.getEndOn()))
            && (this.getIsNotice() == null ? other.getIsNotice() == null : this.getIsNotice().equals(other.getIsNotice()))
            && (this.getSetEndOn() == null ? other.getSetEndOn() == null : this.getSetEndOn().equals(other.getSetEndOn()))
            && (this.getRepeatEveryType() == null ? other.getRepeatEveryType() == null : this.getRepeatEveryType().equals(other.getRepeatEveryType()))
            && (this.getMonthDay() == null ? other.getMonthDay() == null : this.getMonthDay().equals(other.getMonthDay()))
            && (this.getMonthWeekTimes() == null ? other.getMonthWeekTimes() == null : this.getMonthWeekTimes().equals(other.getMonthWeekTimes()))
            && (this.getMonthWeekDay() == null ? other.getMonthWeekDay() == null : this.getMonthWeekDay().equals(other.getMonthWeekDay()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getSsFlag() == null ? other.getSsFlag() == null : this.getSsFlag().equals(other.getSsFlag()))
            && (this.getSsBeforeDays() == null ? other.getSsBeforeDays() == null : this.getSsBeforeDays().equals(other.getSsBeforeDays()))
            && (this.getSsAfterDays() == null ? other.getSsAfterDays() == null : this.getSsAfterDays().equals(other.getSsAfterDays()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getRepeatByDays() == null ? other.getRepeatByDays() == null : this.getRepeatByDays().equals(other.getRepeatByDays()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_repeat")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCustomerId() == null) ? 0 : getCustomerId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getStaffId() == null) ? 0 : getStaffId().hashCode());
        result = prime * result + ((getRepeatType() == null) ? 0 : getRepeatType().hashCode());
        result = prime * result + ((getRepeatEvery() == null) ? 0 : getRepeatEvery().hashCode());
        result = prime * result + ((getRepeatBy() == null) ? 0 : getRepeatBy().hashCode());
        result = prime * result + ((getStartsOn() == null) ? 0 : getStartsOn().hashCode());
        result = prime * result + ((getTimes() == null) ? 0 : getTimes().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getEndOn() == null) ? 0 : getEndOn().hashCode());
        result = prime * result + ((getIsNotice() == null) ? 0 : getIsNotice().hashCode());
        result = prime * result + ((getSetEndOn() == null) ? 0 : getSetEndOn().hashCode());
        result = prime * result + ((getRepeatEveryType() == null) ? 0 : getRepeatEveryType().hashCode());
        result = prime * result + ((getMonthDay() == null) ? 0 : getMonthDay().hashCode());
        result = prime * result + ((getMonthWeekTimes() == null) ? 0 : getMonthWeekTimes().hashCode());
        result = prime * result + ((getMonthWeekDay() == null) ? 0 : getMonthWeekDay().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getSsFlag() == null) ? 0 : getSsFlag().hashCode());
        result = prime * result + ((getSsBeforeDays() == null) ? 0 : getSsBeforeDays().hashCode());
        result = prime * result + ((getSsAfterDays() == null) ? 0 : getSsAfterDays().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getRepeatByDays() == null) ? 0 : getRepeatByDays().hashCode());
        return result;
    }
}
package opensearch

import (
	"bytes"
	"context"
	"net/http"

	"github.com/bytedance/sonic"
	"github.com/opensearch-project/opensearch-go/opensearchapi"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type CreateIndexRequest struct {
	Settings *CreateIndexRequestSettings `json:"settings"`
}

type CreateIndexRequestSettings struct {
	Index *CreateIndexRequestSettingsIndex `json:"index"`
}

type CreateIndexRequestSettingsIndex struct {
	NumberOfShards   int `json:"number_of_shards"`
	NumberOfReplicas int `json:"number_of_replicas"`
}

func (i *impl) CreateIndex(ctx context.Context, index string, request *CreateIndexRequest) error {
	settings, err := sonic.Marshal(request)
	if err != nil {
		return status.Errorf(codes.Internal, "failed to marshal settings: %v", err)
	}
	res := opensearchapi.IndicesCreateRequest{
		Index: index,
		Body:  bytes.NewReader(settings),
	}

	response, err := res.Do(ctx, i.client)
	if err != nil {
		log.ErrorContext(ctx, "failed to invoke create index api", zap.Error(err), zap.Any("response", response))

		return status.Errorf(codes.Internal, "failed to invoke create index api: %v, response: %v", err, response)
	}

	if response.StatusCode != http.StatusOK {
		log.ErrorContext(ctx, "failed to create index", zap.Error(err), zap.Any("response", response))

		return status.Errorf(codes.Internal, "failed to create index: %v, response: %v", err, response)
	}

	return nil
}

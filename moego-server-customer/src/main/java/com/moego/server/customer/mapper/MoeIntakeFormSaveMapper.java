package com.moego.server.customer.mapper;

import com.moego.server.customer.dto.IntakeFormSubmissionDetailsDTO;
import com.moego.server.customer.mapperbean.MoeIntakeFormSave;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeIntakeFormSaveMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_intake_form_save
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_intake_form_save
     *
     * @mbg.generated
     */
    int insert(MoeIntakeFormSave record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_intake_form_save
     *
     * @mbg.generated
     */
    int insertSelective(MoeIntakeFormSave record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_intake_form_save
     *
     * @mbg.generated
     */
    MoeIntakeFormSave selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_intake_form_save
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeIntakeFormSave record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_intake_form_save
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeIntakeFormSave record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_intake_form_save
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeIntakeFormSave record);

    /**
     * get all submissions in this business
     * @param businessId
     * @return
     */
    List<IntakeFormSubmissionDetailsDTO> getAllReceivedForms(
            @Param("businessId") Integer businessId, @Param("isCreate") Byte isCreate, @Param("isRead") Byte isRead);

    Integer querySubmissionCount(@Param("businessId") Integer businessId);

    MoeIntakeFormSave getFirstForm(
            @Param("businessId") Integer businessId, @Param("phoneSuffix") String phoneSuffix9Digits);

    List<MoeIntakeFormSave> queryIntakeFormSaveByTimeRange(
            @Param("companyId") Long companyId,
            @Param("customerId") Long customerId,
            @Param("startTime") Integer startTime,
            @Param("endTime") Integer endTime);
}

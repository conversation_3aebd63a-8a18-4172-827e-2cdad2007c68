package com.moego.server.customer.web;

import com.github.pagehelper.PageInfo;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.server.customer.dto.AddResultDTO;
import com.moego.server.customer.dto.BusinessAgreementRecordDTO;
import com.moego.server.customer.dto.CustomerAgreementInfoDTO;
import com.moego.server.customer.dto.CustomerAgreementRecordDTO;
import com.moego.server.customer.dto.CustomerSignStatus;
import com.moego.server.customer.params.AddCustomerAgreementParams;
import com.moego.server.customer.params.AddCustomerAgreementUploadParams;
import com.moego.server.customer.params.ClientCommitSignParams;
import com.moego.server.customer.service.CustomerService;
import com.moego.server.customer.service.MoeCustomerAgreementService;
import com.moego.server.customer.service.params.CommonIdsParams;
import com.moego.server.customer.utils.SvcPermissionHelper;
import com.moego.server.customer.web.vo.CustomerAgreementVo;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class MoeCustomerAgreementController {

    @Autowired
    private MoeCustomerAgreementService moeCustomerAgreementService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private MigrateHelper migrateHelper;

    @Autowired
    private SvcPermissionHelper svcPermissionHelper;

    @PostMapping("/customer/agreement")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<AddResultDTO> addCustomerAgreement(
            AuthContext context, @Valid @RequestBody ClientCommitSignParams commitSignParams) {
        AddCustomerAgreementParams customerAgreementVo = new AddCustomerAgreementParams();
        BeanUtils.copyProperties(commitSignParams, customerAgreementVo);
        if (customerAgreementVo.getUnsignedId() != null) {
            return moeCustomerAgreementService.signCustomerAgreement(customerAgreementVo);
        } else if (customerAgreementVo.getAgreementId() != null) {
            return moeCustomerAgreementService.addCustomerAgreement(customerAgreementVo);
        } else {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "agreementId and unsignedId are both null");
        }
    }

    // DONE(Ritchie): 需要校验 customerId -> businessId
    @PostMapping("/customer/agreement/image")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.CREATE,
            resourceType = ResourceType.AGREEMENT_SIGNATURE,
            resourceId = "#result.data.id",
            details = "#customerAgreementVo")
    public ResponseResult<AddResultDTO> addCustomerAgreementUploadImages(
            AuthContext context, @Valid @RequestBody AddCustomerAgreementUploadParams customerAgreementVo) {

        var migrated = migrateHelper.isMigrate(context);
        var companyId = context.companyId();
        var businessId = context.getBusinessId();
        var customerId = customerAgreementVo.getCustomerId();

        customerService.checkingBizByCustomerId(companyId, migrated ? null : businessId, customerId);
        // TODO(account structure): company id?
        customerAgreementVo.setBusinessId(context.getBusinessId());
        return moeCustomerAgreementService.addCustomerAgreementUploadImages(customerAgreementVo);
    }

    /**
     * Frank：由前端确认已弃用
     */
    @PutMapping("/customer/agreement")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Integer> updateCustomerAgreement(
            AuthContext context, @RequestBody CustomerAgreementVo customerAgreementVo) {
        return ResponseResult.fail(ResponseCodeEnum.PARAMS_ERROR);
        /*
        customerAgreementVo.setBusinessId(context.getBusinessId());
        if (customerAgreementVo.getCustomerId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "customer id  not null");
        }
        return moeCustomerAgreementService.updateCustomerAgreement(customerAgreementVo);
        */
    }

    @GetMapping("/customer/agreement/list")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<CustomerAgreementRecordDTO>> queryCustomerAgreementList(
            AuthContext context,
            @RequestParam Integer customerId,
            Integer type,
            String servicesType,
            @RequestParam(required = false) Integer appointmentId,
            @RequestParam(required = false) Integer searchBusinessId) {
        Long company = null;
        if (migrateHelper.isMigrate(context)) {
            company = context.companyId();
        }
        if (searchBusinessId != null) {
            svcPermissionHelper.checkBusinessCompany(context.companyId(), searchBusinessId);
        }
        return ResponseResult.success(moeCustomerAgreementService.queryCustomerAgreementList(
                customerId, context.getBusinessId(), type, servicesType, company, appointmentId, searchBusinessId));
    }

    @GetMapping("/customer/agreement/info")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<CustomerAgreementInfoDTO> queryCustomerAgreementInfo(
            AuthContext context, @RequestParam Integer id) {
        Integer businessId = migrateHelper.isMigrate(context) ? null : context.getBusinessId();
        return moeCustomerAgreementService.queryCustomerAgreementInfo(businessId, id);
    }

    @GetMapping("/customer/agreement/status")
    @Auth(AuthType.BUSINESS)
    public List<CustomerSignStatus> queryCustomerAgreementIsNeedSign(
            AuthContext context, @RequestParam Integer customerId, Integer groomingId) {
        return moeCustomerAgreementService.queryCustomerAgreementIsNeedSign(
                context.getBusinessId(), customerId, groomingId);
    }

    @DeleteMapping("/customer/agreement")
    @Auth(AuthType.COMPANY)
    @ActivityLog(action = Action.DELETE, resourceType = ResourceType.AGREEMENT_SIGNATURE, details = "#commonIdsParams")
    public ResponseResult<Integer> deleteCustomerAgreementInfo(
            AuthContext context, @RequestBody CommonIdsParams commonIdsParams) {
        return moeCustomerAgreementService.deleteCustomerAgreementInfo(
                context.companyId(), context.staffId(), commonIdsParams.getIds());
    }

    /**
     * 查询business agreement 签署记录
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("/customer/business/agreement/list")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<PageInfo<BusinessAgreementRecordDTO>> queryBusinessAgreementRecord(
            AuthContext context,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize) {
        return moeCustomerAgreementService.queryBusinessAgreementRecord(context.getBusinessId(), pageNum, pageSize);
    }
}

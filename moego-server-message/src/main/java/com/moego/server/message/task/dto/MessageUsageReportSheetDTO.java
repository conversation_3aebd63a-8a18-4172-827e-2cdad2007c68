/*
 * @since 2023-12-01 11:12:18
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.message.task.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
@HeadFontStyle(fontHeightInPoints = 12, fontName = "Arial", bold = BooleanEnum.TRUE)
@HeadStyle(
        fillPatternType = FillPatternTypeEnum.NO_FILL,
        borderTop = BorderStyleEnum.NONE,
        borderBottom = BorderStyleEnum.NONE,
        borderLeft = BorderStyleEnum.NONE,
        borderRight = BorderStyleEnum.NONE,
        horizontalAlignment = HorizontalAlignmentEnum.LEFT)
@ContentFontStyle(fontHeightInPoints = 12, fontName = "Arial")
public class MessageUsageReportSheetDTO {
    @ColumnWidth(15)
    @ExcelProperty("Business ID")
    private int businessId;

    @ColumnWidth(30)
    @ExcelProperty("Business name")
    private String businessName;

    @ColumnWidth(50)
    @ExcelProperty("Address")
    private String address;

    @ColumnWidth(20)
    @ExcelProperty("City")
    private String city;

    @ColumnWidth(30)
    @ExcelProperty("Email")
    private String email;

    @ColumnWidth(15)
    @ExcelProperty("smsSent")
    private int smsSent;

    @ColumnWidth(20)
    @ExcelProperty("massTextSizeSum")
    private int massTextSizeSum;

    @ColumnWidth(15)
    @ExcelProperty("smsSend2Way")
    private int smsSend2Way;

    @ColumnWidth(15)
    @ExcelProperty("smsSendAuto")
    private int smsSendAuto;
}

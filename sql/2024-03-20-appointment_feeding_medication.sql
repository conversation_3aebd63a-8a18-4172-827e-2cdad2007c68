CREATE TABLE IF NOT EXISTS moe_grooming.`appointment_pet_feeding`
(
  `id`                  bigint       NOT NULL AUTO_INCREMENT,
  `company_id`          bigint       NOT NULL DEFAULT '0',
  `appointment_id`      bigint       NOT NULL DEFAULT '0',
  `pet_detail_id`       bigint       NOT NULL DEFAULT '0',
  `pet_id`              bigint       NOT NULL DEFAULT '0',
  `feeding_amount`      varchar(255) NOT NULL DEFAULT '' COMMENT 'such as 1.2, 1/2, 1 etc.',
  `feeding_unit`        varchar(255) NOT NULL DEFAULT '' COMMENT 'pet_metadata.metadata_value, metadata_name = 2',
  `feeding_type`        varchar(255) NOT NULL DEFAULT '' COMMENT 'pet_metadata.metadata_value, metadata_name = 3',
  `feeding_source`      varchar(255) NOT NULL DEFAULT '' COMMENT 'pet_metadata.metadata_value, metadata_name = 4',
  `feeding_instruction` varchar(255) NOT NULL DEFAULT '' COMMENT 'pet_metadata.metadata_value, metadata_name = 5',
  `created_at`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at`          datetime     NULL     DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_appointment` (`company_id`, `appointment_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE IF NOT EXISTS moe_grooming.`appointment_pet_medication`
(
  `id`                bigint       NOT NULL AUTO_INCREMENT,
  `company_id`        bigint       NOT NULL DEFAULT '0',
  `appointment_id`    bigint       NOT NULL DEFAULT '0',
  `pet_detail_id`     bigint       NOT NULL DEFAULT '0',
  `pet_id`            bigint       NOT NULL DEFAULT '0',
  `medication_amount` varchar(255) NOT NULL DEFAULT '' COMMENT 'such as 1.2, 1/2, 1 etc.',
  `medication_unit`   varchar(255) NOT NULL DEFAULT '' COMMENT 'pet_metadata.metadata_value, metadata_name = 7',
  `medication_name`   varchar(255) NOT NULL DEFAULT '' COMMENT 'medication name, user input',
  `medication_note`   varchar(255) NOT NULL DEFAULT '' COMMENT 'medication note, user input',
  `created_at`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at`        datetime     NULL     DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_appointment` (`company_id`, `appointment_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


CREATE TABLE IF NOT EXISTS moe_grooming.`appointment_pet_schedule_setting`
(
  `id`                  bigint NOT NULL AUTO_INCREMENT,
  `company_id`          bigint NOT NULL DEFAULT '0',
  `appointment_id`      bigint NOT NULL DEFAULT '0',
  `schedule_type`       int    NOT NULL DEFAULT '0' COMMENT '1-feeding, 2-medication',
  `schedule_id`         bigint NOT NULL DEFAULT '0' COMMENT 'appointment_pet_feeding.id, appointment_pet_medication.id',
  `schedule_time`       int    NOT NULL DEFAULT '0' COMMENT 'Scheduled time, unit in minutes. 09:00 AM = 540, 09:30 AM = 570 etc.',
  `schedule_extra_json` json   NOT NULL DEFAULT (json_object()) COMMENT 'Schedule extra information, such as schedule alias name etc.',
  PRIMARY KEY (`id`),
  KEY `idx_appointment` (`company_id`, `appointment_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

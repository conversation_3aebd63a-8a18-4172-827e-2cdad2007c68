package com.moego.server.business.web;

import static com.moego.server.grooming.dto.BookOnlineDTO.AvailableTimeType.BY_WORKING_HOURS;

import com.moego.idl.service.online_booking.v1.GetStaffAvailabilityStatusRequest;
import com.moego.idl.service.online_booking.v1.OBStaffAvailabilityServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.StaffTime;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.service.StaffWorkingHourService;
import com.moego.server.business.web.param.ListStaffWorkingHourRangeParam;
import com.moego.server.grooming.api.IBookOnlineAvailableStaffService;
import com.moego.server.grooming.api.IBookOnlineStaffTimeService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.BookOnlineStaffTimeDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.util.LinkedCaseInsensitiveMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/staff-working-hour-range")
public class StaffWorkingRangeController {

    private final StaffWorkingHourService staffWorkingHourService;
    private final IGroomingOnlineBookingService obApi;
    private final IBookOnlineStaffTimeService bookOnlineStaffTimeApi;
    private final IBookOnlineAvailableStaffService bookOnlineAvailableStaffApi;
    private final OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceBlockingStub
            obStaffAvailabilityServiceBlockingStub;

    @PostMapping("/list")
    @Auth(AuthType.ANONYMOUS)
    public Map<Integer, Map<String, TimeRangeDto>> listStaffWorkingHourRange(
            OBAnonymousParams obAnonymousParams, @Valid @RequestBody ListStaffWorkingHourRangeParam param) {
        Integer businessId =
                obApi.mustGetBusinessDTOByOBNameOrDomain(obAnonymousParams).getBusinessId();

        Set<Integer> staffIds = ObjectUtils.isEmpty(param.getStaffIds())
                ? getAvailableStaffIds(businessId)
                : Set.copyOf(param.getStaffIds());

        if (ObjectUtils.isEmpty(staffIds)) {
            return Map.of();
        }

        // 获取 staff management 的 working hour
        Map<Integer, Map<String, TimeRangeDto>> staffWorkingHour =
                getStaffWorkingHour(businessId, List.copyOf(staffIds), param.getStartDate(), param.getEndDate());

        // 只有 Working hours 才需要合并
        BookOnlineDTO obSetting = obApi.getOBSetting(businessId);
        if (!Objects.equals(obSetting.getAvailableTimeType(), BY_WORKING_HOURS)) {
            return staffWorkingHour;
        }

        mergeStaffTime(businessId, staffIds, staffWorkingHour);

        return staffWorkingHour;
    }

    private Set<Integer> getAvailableStaffIds(Integer businessId) {
        return bookOnlineAvailableStaffApi.getAvailableStaffListInAvailabilityType(businessId).stream()
                .map(MoeStaffDto::getId)
                .collect(Collectors.toSet());
    }

    private void mergeStaffTime(
            Integer businessId, Set<Integer> staffIds, Map<Integer, Map<String, TimeRangeDto>> staffWorkingHour) {
        /*
         * 和 OB Availability 的 staff time 合并
         * - 如果启用了 syncWithWorkingHour，直接使用 staff management 的 working hour
         * - 如果没有启用 syncWithWorkingHour，使用 OB Availability 的 staff time
         */
        Map<Integer, BookOnlineStaffTimeDTO> staffIdToTime =
                bookOnlineStaffTimeApi.listBookOnlineStaffTimeByBusinessId(businessId).stream()
                        .filter(staffTime -> staffIds.contains(staffTime.getStaffId()))
                        .collect(
                                Collectors.toMap(BookOnlineStaffTimeDTO::getStaffId, Function.identity(), (o, n) -> o));

        var availableTimeSync = bookOnlineAvailableStaffApi.bookOnlineAvailableTimeSync(businessId);

        var response = obStaffAvailabilityServiceBlockingStub.getStaffAvailabilityStatus(
                GetStaffAvailabilityStatusRequest.newBuilder()
                        .setBusinessId(businessId)
                        .build());

        removeOBDisabledStaff(staffWorkingHour, response.getStaffAvailabilityMap());

        staffWorkingHour.forEach((staffId, dateToRange) -> {
            if (!availableTimeSync) {
                List<String> disabledDays = new ArrayList<>();
                dateToRange.forEach((date, staffRange) -> {
                    BookOnlineStaffTimeDTO staffTime = staffIdToTime.get(staffId);
                    if (staffTime != null) {
                        Map<String, StaffTime> dayToTime = new LinkedCaseInsensitiveMap<>();
                        dayToTime.putAll(
                                Optional.ofNullable(staffTime.getStaffTimes()).orElseGet(Map::of));
                        StaffTime time = dayToTime.get(
                                LocalDate.parse(date).getDayOfWeek().name());
                        if (time == null) {
                            return;
                        }
                        // disabled
                        if (Boolean.FALSE.equals(time.getIsSelected())) {
                            disabledDays.add(date);
                            return;
                        }
                        if (!time.getTimeRange().isEmpty()) {
                            staffRange.setStartTime(time.getTimeRange().get(0).getStartTime());
                            staffRange.setEndTime(time.getTimeRange()
                                    .get(time.getTimeRange().size() - 1)
                                    .getEndTime());
                        }
                    }
                });
                disabledDays.forEach(dateToRange::remove);
            }
        });
    }

    private static void removeOBDisabledStaff(
            Map<Integer, Map<String, TimeRangeDto>> staffWorkingHour, Map<Long, Boolean> staffAvailableMap) {
        for (Map.Entry<Long, Boolean> entry : staffAvailableMap.entrySet()) {
            if (!entry.getValue()) {
                staffWorkingHour.remove(entry.getKey().intValue());
            }
        }
    }

    private Map<Integer, Map<String, TimeRangeDto>> getStaffWorkingHour(
            Integer businessId, List<Integer> staffIds, String startDate, String endDate) {
        Map<Integer, Map<String, TimeRangeDto>> result = new HashMap<>();
        Map<Integer, Map<LocalDate, List<TimeRangeDto>>> staffToWorkingHour =
                staffWorkingHourService.getStaffWithOverrideDate(businessId, staffIds, startDate, endDate);
        for (Map.Entry<Integer, Map<LocalDate, List<TimeRangeDto>>> entry : staffToWorkingHour.entrySet()) {
            Integer staffId = entry.getKey();
            Map<LocalDate, List<TimeRangeDto>> workingHour = entry.getValue();
            Map<String, TimeRangeDto> dayToTimeRange = new TreeMap<>();
            for (Map.Entry<LocalDate, List<TimeRangeDto>> en : workingHour.entrySet()) {
                List<TimeRangeDto> ranges = en.getValue();
                if (!ObjectUtils.isEmpty(ranges)) {
                    dayToTimeRange.put(
                            en.getKey().toString(),
                            new TimeRangeDto(
                                    ranges.get(0).getStartTime(),
                                    ranges.get(ranges.size() - 1).getEndTime()));
                }
            }
            result.put(staffId, dayToTimeRange);
        }
        return result;
    }
}

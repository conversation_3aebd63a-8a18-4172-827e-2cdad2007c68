package com.moego.server.business.service;

import com.moego.common.dto.notificationDto.NotificationExtraClockDto;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.ClockInOutEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.UpdateClockInOutSettingDef;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.GetClockInOutSettingRequest;
import com.moego.idl.service.organization.v1.UpdateClockInOutSettingRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.migrate.MigrateInfo;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.mapper.MoeBusinessClockInOutLogMapper;
import com.moego.server.business.mapper.MoeBusinessMapper;
import com.moego.server.business.mapper.MoeStaffMapper;
import com.moego.server.business.mapperbean.MoeBusiness;
import com.moego.server.business.mapperbean.MoeBusinessClockInOutLog;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.service.dto.ClockInOutRecordDto;
import com.moego.server.business.service.dto.ClockInOutSettingDto;
import com.moego.server.business.service.dto.ClockInOutStaffDto;
import com.moego.server.business.service.dto.ClockInOutStaffSettingDto;
import com.moego.server.business.service.dto.ClockInOutStaffStatusDto;
import com.moego.server.business.service.dto.ClockLogPageDto;
import com.moego.server.business.web.vo.ClockInOutVo;
import com.moego.server.business.web.vo.SaveClockInOutLogVo;
import com.moego.server.business.web.vo.UpdateClockInOutLogVo;
import com.moego.server.business.web.vo.UpdateStaffAccessCodeVo;
import com.moego.server.message.client.INotificationClient;
import com.moego.server.message.params.notification.NotificationClockInParams;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ClockInOutService {

    private final MoeBusinessClockInOutLogMapper clockInOutLogMapper;
    private final MoeBusinessMapper businessMapper;
    private final StaffService staffService;
    private final MoeStaffMapper staffMapper;
    private final INotificationClient iNotificationClient;
    private final MigrateHelper migrateHelper;
    private final CompanyServiceGrpc.CompanyServiceBlockingStub companyClient;

    public static final byte INVALID = 2;

    public MoeBusinessClockInOutLog getBusinessClockInLog(Integer businessId, Integer logId) {
        MoeBusinessClockInOutLog log = clockInOutLogMapper.selectByPrimaryKey(logId);
        return log == null || !log.getBusinessId().equals(businessId) ? null : log;
    }

    public ClockInOutSettingDto getClockInOutSetting(Integer businessId, Integer pageNum, Integer pageSize) {
        MoeBusiness business = businessMapper.selectByPrimaryKey(businessId);
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        ClockInOutSettingDto result = getClockInOutSetting(migrateInfo, business);
        // 获取今天的打卡记录
        ClockInOutRecordDto recordDto = new ClockInOutRecordDto();
        String clockDate = DateUtil.convertTimeZone("yyyy-MM-dd", new Date(), business.getTimezoneName());
        recordDto.setClockDate(clockDate);
        recordDto.setLogInfoList(getLogList(businessId, clockDate, pageNum, pageSize));
        result.setClockInOutRecord(recordDto);
        // 获取staffCodeList
        result.setStaffCodeList(getStaffAccessCode(businessId, migrateInfo));
        return result;
    }

    public Boolean updateClockInOutSetting(Integer businessId, UpdateStaffAccessCodeVo updateVo, Integer tokenStaffId) {
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        if (migrateInfo.isMigrate()) {
            if (updateVo.getClockInOutEnable() != null || updateVo.getClockInOutNotify() != null) {
                var updateClockInOutSetting = UpdateClockInOutSettingDef.newBuilder();
                if (updateVo.getClockInOutEnable() != null) {
                    updateClockInOutSetting.setEnableClockInOut(
                            updateVo.getClockInOutEnable().equals(BooleanEnum.VALUE_TRUE));
                }
                if (updateVo.getClockInOutNotify() != null) {
                    updateClockInOutSetting.setClockInOutNotify(
                            updateVo.getClockInOutNotify().equals(BooleanEnum.VALUE_TRUE));
                }

                var response = companyClient.updateClockInOutSetting(UpdateClockInOutSettingRequest.newBuilder()
                        .setTokenCompanyId(migrateInfo.companyId())
                        .setTokenStaffId(tokenStaffId)
                        .setClockInOutSetting(updateClockInOutSetting.build())
                        .build());

                return response.getSuccess();
            }
            // isEnableAccessCode 字段下放到 staff 表，迁移后老接口不支持更新
            return false;
        }
        MoeBusiness business = new MoeBusiness();
        business.setId(businessId);
        business.setUpdateTime(DateUtil.get10Timestamp());
        if (updateVo.getClockInOutEnable() != null) {
            business.setClockInOutEnable(updateVo.getClockInOutEnable());
        }
        if (updateVo.getClockInOutNotify() != null) {
            business.setClockInOutNotify(updateVo.getClockInOutNotify());
        }
        if (updateVo.getIsEnableAccessCode() != null) {
            business.setIsEnableAccessCode(updateVo.getIsEnableAccessCode());
        }
        businessMapper.updateByPrimaryKeySelective(business);
        return true;
    }

    /**
     * 查询 staff access code list
     *
     * @param businessId businessId
     * @param migrateInfo business migrateInfo
     * @return List<ClockInOutStaffDto>
     */
    public List<ClockInOutStaffDto> getStaffAccessCode(Integer businessId, MigrateInfo migrateInfo) {
        //        List<MoeStaff> staffList = staffMapper.getStaffListByBusinessId(businessId);
        List<MoeStaff> staffList = staffService.getStaffListByBusinessId(businessId, false, migrateInfo);
        List<ClockInOutStaffDto> staffDtoList = new ArrayList<>();
        staffList.forEach(staff -> {
            ClockInOutStaffDto staffDto = new ClockInOutStaffDto();
            staffDto.setStaffId(staff.getId());
            staffDto.setAccessCode(staff.getAccessCode());
            staffDto.setFirstName(staff.getFirstName());
            staffDto.setLastName(staff.getLastName());
            staffDto.setRequireAccessCode(staff.getRequireAccessCode());
            staffDtoList.add(staffDto);
        });
        return staffDtoList;
    }

    public ClockLogPageDto getLogList(
            Integer businessId, Integer staffId, String startDate, String endDate, Integer pageNum, Integer pageSize) {
        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize == 0) {
            pageSize = 100;
        }
        Integer offset = CommonUtil.getLimitOffset(pageNum, pageSize);
        ClockLogPageDto logPageDto = new ClockLogPageDto();
        logPageDto.setLogList(clockInOutLogMapper.selectByClockDate(
                List.of(businessId), staffId, startDate, endDate, offset, pageSize));
        // 查询、计算总的打卡时长，保留一位小数，四舍五入
        Long totalClockTime =
                clockInOutLogMapper.calculateClockTimeByClockDate(businessId, staffId, startDate, endDate);
        if (totalClockTime == null) {
            totalClockTime = 0L;
        }
        Double totalClockHours = BigDecimal.valueOf(totalClockTime / 3600d)
                .setScale(1, RoundingMode.HALF_UP)
                .doubleValue();
        logPageDto.setTotalHours(totalClockHours);

        logPageDto.setTotalCount(
                clockInOutLogMapper.selectByClockDateCount(List.of(businessId), staffId, startDate, endDate));
        if (logPageDto.getTotalCount() == null) {
            logPageDto.setTotalCount(0);
        }
        return logPageDto;
    }

    public ClockLogPageDto getLogList(Integer businessId, String date, Integer pageNum, Integer pageSize) {
        return getLogList(businessId, null, date, date, pageNum, pageSize);
    }

    public Boolean updateStaffCode(Long companyId, Integer staffId, String newAccessCode) {
        if (staffService.getCompanyStaff(companyId, staffId) == null) {
            return false;
        }
        MoeStaff staff = new MoeStaff();
        staff.setId(staffId);
        staff.setAccessCode(newAccessCode);
        staffMapper.updateByPrimaryKeySelective(staff);
        return true;
    }

    /**
     * staffId 当天打卡
     *
     * @param params params
     * @return r
     */
    public Boolean clockInOut(ClockInOutVo params) {
        Long tokenCompanyId = params.getTokenCompanyId();
        Integer tokenBusinessId = params.getTokenBusinessId();
        Integer staffId = params.getStaffId();
        Integer operateType = params.getOperateType();
        String date = params.getDate();
        Integer clockInOutBusinessId;

        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(tokenBusinessId);
        if (migrateInfo.isMigrate() && params.getBusinessId() != null) {
            // 迁移后，可以指定 businessId 打卡
            clockInOutBusinessId = params.getBusinessId();
        } else {
            // 迁移前不支持指定 businessId 打卡，只能打卡当前 businessId
            clockInOutBusinessId = tokenBusinessId;
        }
        MoeBusiness business = businessMapper.selectByPrimaryKey(clockInOutBusinessId);
        // 校验 businessId 是否合法
        if (business == null || business.getCompanyId().longValue() != tokenCompanyId) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "can't find target business info");
        }

        // 获取打卡设置
        ClockInOutSettingDto clockInOutSetting = getClockInOutSetting(migrateInfo, business);
        boolean enableOvernight = BooleanEnum.VALUE_TRUE.equals(clockInOutSetting.getEnableClockInOutOvernight());

        // 获取打卡记录
        MoeBusinessClockInOutLog clockInOutLog =
                getClockInOutLog(clockInOutBusinessId, staffId, date, operateType, enableOvernight);

        MoeStaff staffInfo = staffService.getCompanyStaff(tokenCompanyId, staffId);
        if (staffInfo == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "can't find target staff info");
        }
        Long currentTime = DateUtil.get10Timestamp();
        NotificationExtraClockDto clockDto = new NotificationExtraClockDto();
        double clockHours = 0.0;
        if (clockInOutLog == null || !clockInOutLog.getClockOutTime().equals((long) 0)) {
            // clockIn, operateType need 1
            if (!operateType.equals(ClockInOutEnum.OPERATE_TYPE_CLOCK_IN)) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "clock out error");
            }
            clockInOutLog = new MoeBusinessClockInOutLog();
            clockInOutLog.setCompanyId(tokenCompanyId);
            clockInOutLog.setBusinessId(clockInOutBusinessId);
            clockInOutLog.setAccountId(staffInfo.getAccountId());
            clockInOutLog.setStaffId(staffId);
            clockInOutLog.setClockDate(date);
            clockInOutLog.setClockInTime(currentTime);
            clockInOutLog.setUpdateTime(currentTime);
            clockInOutLogMapper.insertSelective(clockInOutLog);
            clockDto.setClockType(ClockInOutEnum.OPERATE_TYPE_CLOCK_IN);
        } else {
            // clockOut, operateType need 2
            if (!operateType.equals(ClockInOutEnum.OPERATE_TYPE_CLOCK_OUT)) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "clock in error");
            }
            clockInOutLog.setClockOutTime(currentTime);
            clockInOutLog.setUpdateTime(currentTime);
            clockInOutLogMapper.updateByPrimaryKeySelective(clockInOutLog);
            // prepare notify param
            clockDto.setClockType(ClockInOutEnum.OPERATE_TYPE_CLOCK_OUT);
            clockHours = ((double) (clockInOutLog.getClockOutTime() - clockInOutLog.getClockInTime())) / 3600;
        }
        if (BooleanEnum.VALUE_TRUE.equals(clockInOutSetting.getClockInOutNotify())) {
            Double finalClockHours = clockHours;
            ThreadPool.execute(() -> {
                // 调用通知发送
                NotificationClockInParams clockInParams = new NotificationClockInParams();
                clockInParams.setBusinessId(clockInOutBusinessId);
                clockDto.setStaffId(staffId);
                clockDto.setOwnerStaffId(staffService.getOwnerStaffId(clockInOutBusinessId));
                clockDto.setStaffName(staffInfo.getFirstName());
                clockInParams.setWebPushDto(clockDto);
                clockInParams.formatPushBody(
                        DateUtil.convertTimeBySeconds(
                                currentTime, business.getTimezoneName(), business.getTimeFormatType()),
                        finalClockHours);
                iNotificationClient.sendNotificationClockIn(clockInParams);
            });
        }
        return true;
    }

    /**
     * 获取staff 当天 最新 打卡记录
     *
     * @param businessId businessId
     * @param staffId    staffId
     * @return r
     */
    public MoeBusinessClockInOutLog getStaffRecord(Integer businessId, Integer staffId, String clockDate) {
        return clockInOutLogMapper.selectLatestLog(businessId, staffId, clockDate);
    }

    /**
     * 获取员工最近一次未完成的打卡记录（clockOutTime为0的记录）
     * 用于支持跨天打卡功能
     *
     * @param businessId 业务ID
     * @param staffId 员工ID
     * @return 未完成的打卡记录
     */
    public MoeBusinessClockInOutLog getStaffUnfinishedRecord(Integer businessId, Integer staffId) {
        return clockInOutLogMapper.selectLatestUnfinishedLog(businessId, staffId);
    }

    /**
     * 获取前一天未完成的打卡记录（clockOutTime为0的记录）
     * 用于支持跨天打卡功能
     *
     * @param businessId 业务ID
     * @param staffId 员工ID
     * @param date 当前日期
     * @return 前一天未完成的打卡记录
     */
    @Nullable
    public MoeBusinessClockInOutLog getPreviousDayUnfinishedRecord(Integer businessId, Integer staffId, String date) {
        if (staffId == null || !StringUtils.hasText(date)) {
            return null;
        }

        // 计算前一天的日期
        LocalDate previousDay = LocalDate.parse(date).minusDays(1);
        String previousDate = previousDay.toString();

        // 查询前一天的打卡记录
        MoeBusinessClockInOutLog previousDayLog =
                clockInOutLogMapper.selectLatestLog(businessId, staffId, previousDate);

        // 如果前一天有打卡记录，且未打卡下班（clockOutTime为0），则返回该记录
        if (previousDayLog != null && previousDayLog.getClockOutTime().equals(0L)) {
            return previousDayLog;
        }

        return null;
    }

    /**
     * 根据操作类型获取打卡记录
     *
     * @param businessId 业务ID
     * @param staffId 员工ID
     * @param date 打卡日期
     * @param operateType 操作类型（上班/下班）
     * @param enableOvernight 是否启用跨天打卡
     * @return 打卡记录
     */
    private MoeBusinessClockInOutLog getClockInOutLog(
            Integer businessId, Integer staffId, String date, Integer operateType, boolean enableOvernight) {
        MoeBusinessClockInOutLog clockInOutLog;
        if (operateType.equals(ClockInOutEnum.OPERATE_TYPE_CLOCK_IN)) {
            // 打卡上班，查找当天的记录
            clockInOutLog = getStaffRecord(businessId, staffId, date);
        } else {
            // 打卡下班
            if (enableOvernight) {
                // 启用跨天打卡，查找未完成的记录
                clockInOutLog = getStaffUnfinishedRecord(businessId, staffId);
                // 如果没有未完成的记录，再查找当天的记录
                if (clockInOutLog == null) {
                    clockInOutLog = getStaffRecord(businessId, staffId, date);
                }
            } else {
                // 不启用跨天打卡，只查找当天的记录
                clockInOutLog = getStaffRecord(businessId, staffId, date);
            }
        }
        return clockInOutLog;
    }

    private void ensureClockOutTimeIsAfterClockInTime(Long clockIn, Long clockOut) {
        if (clockIn > clockOut) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "Clock in time is before clock out time.");
        }
    }

    public Boolean updateLogTime(Integer businessId, UpdateClockInOutLogVo logVo) {
        if (null == getBusinessClockInLog(businessId, logVo.getLogId())) {
            return false;
        }
        ensureClockOutTimeIsAfterClockInTime(logVo.getClockInTime(), logVo.getClockOutTime());
        MoeBusinessClockInOutLog log = new MoeBusinessClockInOutLog();
        log.setId(logVo.getLogId());
        log.setClockInTime(logVo.getClockInTime());
        log.setClockOutTime(logVo.getClockOutTime());
        log.setUpdateTime(DateUtil.get10Timestamp());
        clockInOutLogMapper.updateByPrimaryKeySelective(log);
        return true;
    }

    public Integer insertLog(Long companyId, Integer businessId, SaveClockInOutLogVo logVo) {
        ensureClockOutTimeIsAfterClockInTime(logVo.getClockInTime(), logVo.getClockOutTime());
        MoeStaff staffInfo = staffService.getCompanyStaff(companyId, logVo.getStaffId());
        if (staffInfo == null) {
            return null;
        }
        MoeBusinessClockInOutLog clockInOutLog = new MoeBusinessClockInOutLog();
        clockInOutLog.setCompanyId(companyId);
        clockInOutLog.setBusinessId(businessId);
        clockInOutLog.setAccountId(staffInfo.getAccountId());
        clockInOutLog.setStaffId(staffInfo.getId());
        clockInOutLog.setClockDate(logVo.getClockDate());
        clockInOutLog.setClockInTime(logVo.getClockInTime());
        clockInOutLog.setClockOutTime(logVo.getClockOutTime());
        clockInOutLog.setUpdateTime(DateUtil.get10Timestamp());
        clockInOutLogMapper.insertSelective(clockInOutLog);
        return clockInOutLog.getId();
    }

    public Boolean deleteLog(Integer businessId, UpdateClockInOutLogVo logVo) {
        if (Objects.isNull(getBusinessClockInLog(businessId, logVo.getLogId()))) {
            return false;
        }
        MoeBusinessClockInOutLog log = new MoeBusinessClockInOutLog();
        log.setId(logVo.getLogId());
        log.setStatus(INVALID);
        log.setUpdateTime(DateUtil.get10Timestamp());
        return clockInOutLogMapper.updateByPrimaryKeySelective(log) > 0;
    }

    public ClockInOutStaffSettingDto getStaffClockInOut(Integer businessId, String date) {
        ClockInOutStaffSettingDto result = new ClockInOutStaffSettingDto();
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        boolean isMigrate = migrateInfo.isMigrate();

        // 获取打卡设置，包括是否启用跨天打卡
        ClockInOutSettingDto clockInOutSetting = null;
        boolean enableOvernight = false;

        if (isMigrate) {
            var response = companyClient.getClockInOutSetting(GetClockInOutSettingRequest.newBuilder()
                    .setTokenCompanyId(migrateInfo.companyId())
                    .build());
            result.setClockInOutEnable(
                    response.getClockInOutSetting().getEnableClockInOut()
                            ? BooleanEnum.VALUE_TRUE
                            : BooleanEnum.VALUE_FALSE);

            // 获取是否启用跨天打卡
            enableOvernight = response.getClockInOutSetting().getEnableClockInOutOvernight();
        } else {
            // 获取clockInOut状态
            MoeBusiness business = businessMapper.selectByPrimaryKey(businessId);
            result.setClockInOutEnable(business.getClockInOutEnable());
            result.setIsEnableAccessCode(business.getIsEnableAccessCode());

            // 对于未迁移的用户，默认不启用跨天打卡
            enableOvernight = false;
        }

        // 获取staff当天打开状态
        List<ClockInOutStaffDto> staffDtoList = getStaffAccessCode(businessId, migrateInfo);
        boolean finalEnableOvernight = enableOvernight;
        List<ClockInOutStaffStatusDto> staffStatusDtoList = staffDtoList.stream()
                .map(staffDto -> {
                    ClockInOutStaffStatusDto staffStatusDto = new ClockInOutStaffStatusDto();
                    BeanUtils.copyProperties(staffDto, staffStatusDto);
                    if (!isMigrate) {
                        // 迁移前的 requireAccessCode，取 business level 的值
                        staffStatusDto.setRequireAccessCode(
                                BooleanEnum.VALUE_TRUE.equals(result.getIsEnableAccessCode()));
                    }

                    boolean isClockIn = false;
                    boolean isClockOut = false;
                    // todo 这里在循环里查询，待优化
                    MoeBusinessClockInOutLog logRecord = getStaffRecord(businessId, staffStatusDto.getStaffId(), date);

                    // 如果启用了跨天打卡且当天没有打卡记录，查询前一天未完成的打卡记录
                    if (finalEnableOvernight
                            && (logRecord == null || logRecord.getClockInTime().equals((long) 0))) {
                        MoeBusinessClockInOutLog previousDayUnfinishedLog =
                                getPreviousDayUnfinishedRecord(businessId, staffStatusDto.getStaffId(), date);
                        if (previousDayUnfinishedLog != null) {
                            logRecord = previousDayUnfinishedLog;
                        }
                    }

                    if (logRecord != null) {
                        if (!logRecord.getClockInTime().equals((long) 0)) {
                            isClockIn = true;
                        }
                        if (!logRecord.getClockOutTime().equals((long) 0)) {
                            isClockOut = true;
                        }
                        staffStatusDto.setClockInTime(logRecord.getClockInTime());
                        staffStatusDto.setClockOutTime(logRecord.getClockOutTime());
                    } else {
                        staffStatusDto.setClockInTime((long) 0);
                        staffStatusDto.setClockOutTime((long) 0);
                    }
                    staffStatusDto.setIsClockIn(isClockIn);
                    staffStatusDto.setIsClockOut(isClockOut);
                    return staffStatusDto;
                })
                .toList();
        result.setStaffStatusList(staffStatusDtoList);
        return result;
    }

    private ClockInOutSettingDto getClockInOutSetting(MigrateInfo migrateInfo, MoeBusiness business) {
        ClockInOutSettingDto clockInOutSetting = new ClockInOutSettingDto();
        if (migrateInfo.isMigrate()) {
            var response = companyClient.getClockInOutSetting(GetClockInOutSettingRequest.newBuilder()
                    .setTokenCompanyId(migrateInfo.companyId())
                    .build());
            clockInOutSetting.setClockInOutEnable(
                    response.getClockInOutSetting().getEnableClockInOut()
                            ? BooleanEnum.VALUE_TRUE
                            : BooleanEnum.VALUE_FALSE);
            clockInOutSetting.setClockInOutNotify(
                    response.getClockInOutSetting().getClockInOutNotify()
                            ? BooleanEnum.VALUE_TRUE
                            : BooleanEnum.VALUE_FALSE);
            clockInOutSetting.setEnableClockInOutOvernight(
                    response.getClockInOutSetting().getEnableClockInOutOvernight()
                            ? BooleanEnum.VALUE_TRUE
                            : BooleanEnum.VALUE_FALSE);
        } else {
            // 获取clockInOut状态
            clockInOutSetting.setClockInOutEnable(business.getClockInOutEnable());
            clockInOutSetting.setClockInOutNotify(business.getClockInOutNotify());
            clockInOutSetting.setIsEnableAccessCode(business.getIsEnableAccessCode());
            // 对于未迁移的用户，默认不启用跨天打卡
            clockInOutSetting.setEnableClockInOutOvernight(BooleanEnum.VALUE_FALSE);
        }
        return clockInOutSetting;
    }
}
